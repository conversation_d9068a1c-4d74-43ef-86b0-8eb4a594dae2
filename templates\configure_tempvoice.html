{% extends "base.html" %}

{% block title %}Configure Temp Voice - {{ server_info.name }}{% endblock %}

{% block content %}
<div class="row mt-3">
    <!-- Sidebar -->
    <div class="col-md-3">
        <div class="sidebar p-3">
            <h6 class="text-muted text-uppercase mb-3">Configuration</h6>
            <nav class="nav flex-column">
                <a class="nav-link" href="{{ url_for('dashboard') }}">
                    <i class="fas fa-tachometer-alt"></i>Overview
                </a>
                <a class="nav-link" href="{{ url_for('configure_repping') }}">
                    <i class="fas fa-star"></i>Repping System
                </a>
                <a class="nav-link" href="{{ url_for('configure_vent') }}">
                    <i class="fas fa-heart"></i>Vent System
                </a>
                <a class="nav-link active" href="{{ url_for('configure_tempvoice') }}">
                    <i class="fas fa-microphone"></i>Temp Voice
                </a>
                <a class="nav-link" href="#" onclick="showComingSoon('Sticky Messages')">
                    <i class="fas fa-thumbtack"></i>Sticky Messages
                </a>
                <a class="nav-link" href="#" onclick="showComingSoon('DM Support')">
                    <i class="fas fa-ticket-alt"></i>DM Support
                </a>
                <a class="nav-link" href="#" onclick="showComingSoon('Gender Verification')">
                    <i class="fas fa-shield-alt"></i>Gender Verification
                </a>
                <a class="nav-link" href="{{ url_for('configure_automod') }}">
                    <i class="fas fa-robot"></i>Automod
                </a>
            </nav>
        </div>
    </div>

    <!-- Main Content -->
    <div class="col-md-9">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-microphone text-info me-2"></i>Temp Voice Configuration</h2>
            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
            </a>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Configure Temporary Voice Channels</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="mb-3">
                                <label for="interface_channel_id" class="form-label">Interface Channel ID</label>
                                <input type="text" class="form-control" id="interface_channel_id" name="interface_channel_id" 
                                       value="{{ tempvoice_settings.interface_channel_id if tempvoice_settings else '' }}" 
                                       placeholder="e.g., 123456789012345678" required>
                                <div class="form-text">
                                    The text channel where the TempVoice interface will be posted
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="creator_channel_id" class="form-label">Creator Voice Channel ID</label>
                                <input type="text" class="form-control" id="creator_channel_id" name="creator_channel_id" 
                                       value="{{ tempvoice_settings.creator_channel_id if tempvoice_settings else '' }}" 
                                       placeholder="e.g., 123456789012345678" required>
                                <div class="form-text">
                                    The voice channel users join to automatically create their temporary channel
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="default_user_limit" class="form-label">Default User Limit (Optional)</label>
                                <input type="number" class="form-control" id="default_user_limit" name="default_user_limit" 
                                       value="{{ tempvoice_settings.default_user_limit if tempvoice_settings and tempvoice_settings.default_user_limit else '' }}" 
                                       placeholder="e.g., 10" min="1" max="99">
                                <div class="form-text">
                                    Default user limit for new temporary channels (leave empty for no limit)
                                </div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Save Configuration
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                {% if tempvoice_settings %}
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">Current Configuration</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <strong>Interface Channel:</strong>
                                <br><code>{{ tempvoice_settings.interface_channel_id }}</code>
                            </div>
                            <div class="col-md-4">
                                <strong>Creator Channel:</strong>
                                <br><code>{{ tempvoice_settings.creator_channel_id }}</code>
                            </div>
                            <div class="col-md-4">
                                <strong>Default User Limit:</strong>
                                <br><code>{{ tempvoice_settings.default_user_limit or 'No limit' }}</code>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>

            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>How to Get IDs</h6>
                    </div>
                    <div class="card-body">
                        <ol class="mb-0">
                            <li>Enable Developer Mode in Discord settings</li>
                            <li>Right-click on a channel</li>
                            <li>Select "Copy ID"</li>
                            <li>Paste the ID in the form</li>
                        </ol>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-lightbulb me-2"></i>How It Works</h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-2">The TempVoice system:</p>
                        <ul class="mb-0">
                            <li>Posts an interface in the text channel</li>
                            <li>Users join the creator voice channel</li>
                            <li>Bot automatically creates their temp channel</li>
                            <li>Users get full management controls</li>
                            <li>Channels auto-delete when empty</li>
                        </ul>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-cogs me-2"></i>User Controls</h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-2">Channel owners can:</p>
                        <ul class="mb-0">
                            <li>Set user limits</li>
                            <li>Kick/block users</li>
                            <li>Lock/unlock channels</li>
                            <li>Transfer ownership</li>
                            <li>Rename channels</li>
                        </ul>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-shield-alt me-2"></i>Permissions</h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-2">Temporary channels:</p>
                        <ul class="mb-0">
                            <li>Copy @everyone permissions from creator channel</li>
                            <li>No special permissions for channel owner</li>
                            <li>All permissions come from the creator channel</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function showComingSoon(feature) {
    alert(`${feature} configuration will be available in the web dashboard soon! For now, please use the Discord slash commands.`);
}
</script>
{% endblock %}
